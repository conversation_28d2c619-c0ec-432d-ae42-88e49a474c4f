package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
//import cn.dev33.satoken.same.SaSameUtil;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.from.MessagePolicyForm;
import com.shida.region.biz.pojo.search.MessagePolicySearch;
import com.shida.region.biz.pojo.vo.MessagePolicyInfo;
import com.shida.region.biz.service.IMessagePolicyService;
//import com.shida.userCenter.api.IFeignUserService;
//import com.shida.userCenter.dto.DeptDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
//import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 * @PackageName:com.shida.app.controller.admin
 * @ClassName: MessagePolicyController
 * @Version 1.0
 */
@Tag(name = "政策公告、首页设置")
@RestController
@RequestMapping("/biz/messagePolicy")
@Slf4j
public class MessagePolicyController {

    @Resource
    private IMessagePolicyService messagePolicyService;


    @Operation(summary = "政策公告分页数据")
    @PostMapping("list")
    public PageInfo<MessagePolicyInfo> getPageData(@RequestBody MessagePolicySearch search) {
        return messagePolicyService.getPageData(search);
    }

    @Operation(summary = "政策公告详情")
    @PostMapping("detail")
    public MessagePolicyInfo getDetail(@RequestBody @Valid KeyForm<Long> form){
        return messagePolicyService.getDetail(form.getKey());
    }

    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "新增政策公告")
    @PostMapping("create")
    public Boolean create(@RequestBody @Valid MessagePolicyForm form) {
        return messagePolicyService.create(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "修改政策公告")
    @PostMapping("update")
    public Boolean updateMessage(@RequestBody @Valid MessagePolicyForm form) {
        return messagePolicyService.updateMessage(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "删除政策公告")
    @PostMapping("delete")
    public Boolean deleteById(@RequestBody @Valid KeyForm<Long> form) {
        return messagePolicyService.deleteById(form.getKey());
    }

//    @Resource
//    private IFeignUserService userService;
//    @PostMapping("/test")
//    public void dept(HttpServletRequest httpRequest){
//        DeptDto deptDto = new DeptDto().setIsFive("1");
//        log.info(SaSameUtil.getToken());
//        log.info(httpRequest.getHeader("Authorization").split(" ")[1]);
//        List<DeptDto> list = userService.getDeptList(httpRequest.getHeader("Authorization").split(" ")[1], deptDto);
//        list.forEach(i -> {
//            System.out.println(i.getDeptName());
//        });
//    }
}
