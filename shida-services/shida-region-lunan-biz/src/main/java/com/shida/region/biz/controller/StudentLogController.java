package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.entity.EntitledGroup;
import com.shida.region.biz.pojo.search.StudentLogSearch;
import com.shida.region.biz.pojo.vo.EnrollMiddleFieldVo;
import com.shida.region.biz.pojo.vo.StudentInfoLogVo;
import com.shida.region.biz.pojo.vo.StudentLogVo;
import com.shida.region.biz.service.IStudentLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/10/22 15:14
 */
@Tag(name = "日志相关")
@RestController
@RequestMapping("/biz/studentLog")
public class StudentLogController {

    @Resource
    private IStudentLogService studentLogService;

    @PostMapping("getStudentLogPage")
    @Operation(summary = "超级管理员查询操作日志")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE},mode = SaMode.OR)
    public PageInfo<StudentLogVo> getStudentLogPage(@RequestBody StudentLogSearch search){
        return studentLogService.getStudentLogPage(search);
    }

    @PostMapping("getStudentReviewLog")
    @Operation(summary = "查询学生操作日志")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE},mode = SaMode.OR)
    public List<StudentInfoLogVo> getStudentReviewLog(@RequestBody @Valid KeyForm<Long> form){
        return studentLogService.getStudentReviewLog(form);
    }

    @PostMapping("getStudentInfo")
    @Operation(summary = "查看家长提交往次内容")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.AUDITOR_CODE},mode = SaMode.OR)
    public List<EnrollMiddleFieldVo> getBeforeStudentInfo(@RequestBody @Valid KeyForm<Long> form){
        return studentLogService.getBeforeStudentInfo(form.getKey());
    }
    @PostMapping("getStudentEntitledInfo")
    @Operation(summary = "查看家长提交往次优抚内容")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.AUDITOR_CODE},mode = SaMode.OR)
    public EntitledGroup getStudentEntitledInfo(@RequestBody @Valid KeyForm<Long> form){
        return studentLogService.getStudentEntitledInfo(form.getKey());
    }
}
