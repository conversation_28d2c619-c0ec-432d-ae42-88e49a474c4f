package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.from.SecurityAndHouseNoPassForm;
import com.shida.region.biz.pojo.search.PublicSecurityAndHouseSearch;
import com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo;
import com.shida.region.biz.service.IHouseOwnershipInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;


/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "公安信息核对")
@RestController
@RequestMapping("/biz/security")
public class PublicSecurityController {

    @Resource
    private IHouseOwnershipInfoService houseOwnershipInfoService;

    @SaCheckRole(value = {RoleConstant.POLICE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "公安信息核对分页")
    @PostMapping("page")
    public PageInfo<PublicSecurityAndHousePropertyVo> page(@RequestBody @Valid PublicSecurityAndHouseSearch search) {
        return houseOwnershipInfoService.getPublicSecurityAndHousePropertyPage(search);
    }


    @Operation(summary = "导出公安信息")
    @PostMapping("/export")
    @SaCheckRole(value = {RoleConstant.POLICE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public void exportPoliceInformation(@RequestBody @Valid PublicSecurityAndHouseSearch search, HttpServletResponse response)throws IOException {
        houseOwnershipInfoService.exportPoliceInformation(search,response);
    }

    @Operation(summary = "磁县-导出公安信息")
    @PostMapping("/cxExportSecurity")
    @SaCheckRole(value = {RoleConstant.POLICE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public void cxExport(@RequestBody @Valid PublicSecurityAndHouseSearch search, HttpServletResponse response)throws IOException {
        houseOwnershipInfoService.cxExportSecurity(search,response);
    }

    @PostMapping("import")
    @Operation(summary = "导入公安信息")
    @SaCheckRole(value = {RoleConstant.POLICE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public String importPublicSecurity(@RequestParam("file") MultipartFile file,@RequestParam("publicSecurityReviewType") Integer publicSecurityReviewType)  {
        houseOwnershipInfoService.importPoliceInformation(file,publicSecurityReviewType);
        return "success";
    }

    @PostMapping("passSecurityAndHouseAudit")
    @Operation(summary = "(公安/房管)审核通过")
    @SaCheckRole(value = {RoleConstant.POLICE_CODE,RoleConstant.HOUSE_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    public void passSecurityAndHouseAudit(@RequestBody @Valid SecurityAndHouseNoPassForm form)  {
        houseOwnershipInfoService.passSecurityAndHouseAudit(form);
    }

    @PostMapping("notPassSecurityAndHouseAudit")
    @Operation(summary = "(公安/房管)审核不通过")
    @SaCheckRole(value = {RoleConstant.POLICE_CODE,RoleConstant.HOUSE_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    public void notPassSecurityAndHouseAudit(@RequestBody @Valid SecurityAndHouseNoPassForm form)  {
        houseOwnershipInfoService.notPassSecurityAndHouseAudit(form);
    }



}
