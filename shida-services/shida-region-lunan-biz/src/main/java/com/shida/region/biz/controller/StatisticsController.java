package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.util.ObjectUtil;

import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.search.StatisticsSearch;
import com.shida.region.biz.pojo.vo.PageStatisticsVo;

import com.shida.region.biz.pojo.vo.SchoolStatisticsColumnVo;
import com.shida.region.biz.pojo.vo.SchoolStatisticsVo;
import com.shida.region.biz.service.ILeaseHouseInfoService;
import com.shida.region.biz.service.IStudentBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * Description:
 *
 * @Author: lw
 * @Create: 2024/5/4 - 10:10
 */
@RestController
@RequestMapping("/biz/statistics/")
@Tag(name = "教育局-统计")
public class StatisticsController {
    @Resource
    private IStudentBaseService studentBaseService;
    @Resource
    private ILeaseHouseInfoService leaseHouseInfoService;


    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("pageStatistics")
    @Operation(summary = "首页统计")
    public PageStatisticsVo getPageStatistics(@RequestBody StatisticsSearch search) {
        if (ObjectUtil.isEmpty(search.getPageNumber())) {
            search.setPageNumber(1);
        }
        if (ObjectUtil.isEmpty(search.getPageSize())) {
            search.setPageSize(10);
        }
        return leaseHouseInfoService.getPageStatistics(search);
    }

    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("/schoolStatistics")
    @Operation(summary = "(小学初中)学校统计")
    public PageInfo<SchoolStatisticsVo> getSchoolStatistics(@RequestBody StatisticsSearch search) {
        if (ObjectUtil.isEmpty(search.getPageNumber())) {
            search.setPageNumber(1);
        }
        if (ObjectUtil.isEmpty(search.getPageSize())) {
            search.setPageSize(10);
        }
        return leaseHouseInfoService.getSchoolStatistics(search);
    }

    @PostMapping("/schoolStatisticsColumn")
    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @Operation(summary = "(小学初中)学校统计获取列显示情况")
    public List<SchoolStatisticsColumnVo> getSchoolStatisticsColumn(@RequestBody StatisticsSearch search){
        return leaseHouseInfoService.getSchoolStatisticsColumn(search);
    }

    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("/export")
    @Operation(summary = "导出")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    public void exportSchoolStatistics(@RequestBody StatisticsSearch search, HttpServletResponse response)throws IOException {
        leaseHouseInfoService.exportSchoolStatistics(search,response);
    }

    @SaCheckRole(value = {RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @PostMapping("/currentSchoolStatistics")
    @Operation(summary = "学校账号数据统计")
    public SchoolStatisticsVo currentSchoolStatistics(@RequestBody @Valid KeyForm<Long> schoolId) {
        return leaseHouseInfoService.currentSchoolStatistics(schoolId);
    }


}
