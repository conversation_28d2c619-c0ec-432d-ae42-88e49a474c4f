package com.shida.region.biz;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Date 2024/4/22 9:31
 * @PackageName:com.shida.region.biz
 * @ClassName: RegionBizApplication
 * @Description: TODO
 * @Version 1.0
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.shida"})
@EnableFeignClients(basePackages = "com.shida")
@EnableScheduling
public class LuNanRegionBizApplication {
    public static void main(String[] args) {
        SpringApplication.run(LuNanRegionBizApplication.class, args);
    }
}
