server:
  port: 27202

spring:
  application:
    name: tshgzs-region-biz-service-130202
  servlet:
    multipart:
      max-file-size: 6MB
  config:
    import:
      - optional:nacos:redis.yaml?namespace=tszs-aliyun
      - optional:nacos:mongodb.yaml?namespace=tszs-aliyun
      - optional:nacos:sa-token.yaml?namespace=tszs-aliyun
      - optional:nacos:mysql.yaml?namespace=tszs-aliyun
      - optional:nacos:threadpool.yaml?namespace=tszs-aliyun
  cloud:
    nacos:
      discovery:
        # 开启nacos作为服务注册中心，默认值：true
        enabled: true
        # nacos集群服务注册地址
        server-addr: http://************:8848
        # nacos用户名
        username: nacos
        # nacos密码
        password: GVM5XP72TY
        # 命名空间，默认 public,可设置dev,pro等，相同特征的服务分类，先去nacos命名空间创建
        namespace: tszs-aliyun
        # 分组，默认 DEFAULT_GROUP 相同特征的服务划分的更细
        group: DEFAULT_GROUP
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
  datasource:
    driver-class-name: ${mysql.driver-class-name}
    url: jdbc:mysql://${mysql.host}:${mysql.port}/${region-center.db-name}?useUnicode=true&useSSL=false&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${mysql.username}
    password: ${mysql.password}

knife4j:
  # 开启文档增强
  enable: true
  # 生成时设置为true
  production: false

logging:
  file:
    path: /var/log/${spring.application.name}

# 是否启用前后端安全传输
sd-security:
  # 是否启用
  enable: false
  # 排除urls
  exclude-urls:
    /feign/**

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # 不检查 token 的 url
  not-check-login:
    /doc.html,
    /favicon.ico,
    /favicon.ico,
    /webjars/**,
    /v3/api-docs/**,
    /static/**,
    /user-feign-api/**,
    /securityStatus

# 静态资源
upload:
  # 物理路径配置
  file:
    # 根目录
    root-path: /data/${spring.application.name}/2025/upload/
    # 头像目录
    avatar-path: ${upload.file.root-path}avatar/
    # 图片目录
    image-path: ${upload.file.root-path}images/
    # 其他文件目录
    other-path: ${upload.file.root-path}other/
  url:
    # 头像url
    avatar: /upload/avatar/
    # 图片url
    image: /upload/images/
    # 其他文件url
    other: /upload/other/

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: flag # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

# 每个区县服务单独配置
dept:
  code: 130202
  name: 路南区
  szm: LNQ
  id: 174
  level: 2

user-center:
  db-name: tshg-user-center

region-center:
  db-name: tshg-region-biz-130202
