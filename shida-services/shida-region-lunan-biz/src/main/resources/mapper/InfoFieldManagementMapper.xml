<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.InfoFieldManagementMapper">
    <sql id="Base_Column_List">
    </sql>
    <select id="selectRootAllField" resultType="com.shida.region.biz.entity.InfoFieldManagement">
        SELECT ifm.id,ifm.field_english,ifm.field_name,ifm.input_item_code,ifm.type_config_id
        FROM info_field_management ifm
        LEFT JOIN info_type_config itc ON ifm.type_config_id = itc.id
        WHERE itc.config_id = #{configId,jdbcType=BIGINT}
    </select>
    <select id="selectAllFieldExceptImg" resultType="com.shida.region.biz.pojo.vo.ExcelLeafConfigVo">
        SELECT id,field_english,field_name,input_item_code,type_config_id
        FROM info_field_management
        WHERE input_item_code != 0
    </select>
    <select id="selectSaveField" resultType="com.shida.region.biz.entity.InfoSetExcel">
        SELECT ifm.id AS field_id,
               ifm.type_config_id,
               ifm.field_name,
               ifm.field_english,
               ifm.info_verification_code,
               #{excelId} AS type
        FROM info_field_management ifm
        WHERE ifm.id IN
        <foreach collection="fieldIdList" item="fieldId" open="(" separator="," close=")">
            #{fieldId}
        </foreach>
    </select>
</mapper>
