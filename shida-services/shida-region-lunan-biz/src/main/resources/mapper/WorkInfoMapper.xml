<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.WorkInfoMapper">

    <update id="updateDeleteFlag">
        UPDATE r_work_info
        SET delete_flag = 0
        WHERE student_id = #{studentId}
    </update>
    <delete id="trueDeleteById">
        DELETE FROM r_work_info
        WHERE student_id = #{studentId}
    </delete>
    <select id="getEnrollEntrance" resultType="com.shida.region.biz.pojo.vo.SetupSaveVo">
        SELECT t3.setup_id,t3.ids_name
        FROM t_registration_system_setup t1
        LEFT JOIN t_registration_system_setup t2
            ON t1.parent_id = t2.parent_id
        RIGHT JOIN t_registration_system_set_up_sava t3
            ON t2.id = t3.setup_id
        WHERE t1.id = #{setupId,jdbcType=BIGINT}
    </select>
    <select id="selectSchoolEnrollIds" resultType="com.shida.region.biz.entity.RegistrationSystemSetUpSava">
        SELECT t4.setup_id,t4.ids_name
        FROM t_registration_system_setup t1
        LEFT JOIN t_registration_system_setup t2
        ON t2.parent_id = t1.id
        LEFT JOIN t_registration_system_setup t3
            ON t3.parent_id = t2.id
        LEFT JOIN t_registration_system_set_up_sava t4
            ON t4.path = t3.id
        WHERE t1.name = #{nature}
            AND t2.name = #{type}
                AND t3.name = #{period}
    </select>
</mapper>
