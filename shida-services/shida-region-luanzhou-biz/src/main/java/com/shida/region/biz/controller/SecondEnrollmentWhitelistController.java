package com.shida.region.biz.controller;

import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.pojo.vo.RestMessage;
import com.shida.region.biz.pojo.from.SecondEnrollmentWhitelistForm;
import com.shida.region.biz.pojo.vo.SecondEnrollmentWhitelistVo;
import com.shida.region.biz.service.ISecondEnrollmentWhitelistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 二次报名白名单表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2024-04-30
 */
@RestController
@RequestMapping("/biz/system/secondWhitelist")
@Tag(name = "二次报名白名单管理")
public class SecondEnrollmentWhitelistController {

    @Resource
    private ISecondEnrollmentWhitelistService secondEnrollmentWhitelistService;

    // TODO：对象自己写 不对外暴露 写vo service之间船传值使用pojo的dto  逻辑尽可能写在service中 查询对象分页时使用 Search单一条件的时候 当不满足继承添加新的字段数学
    /**
     * 分页查询白名单列表
     */
    @PostMapping("/pageList")
    @Operation(summary = "分页查询白名单列表")
    public RestMessage<PageInfo<SecondEnrollmentWhitelistVo>> pageList(@RequestBody Search search) {
        return RestMessage.success(secondEnrollmentWhitelistService.pageList(search));
    }

    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @PostMapping("/create")
    @Operation(summary = "新增白名单")
    public boolean create(@RequestBody @Valid SecondEnrollmentWhitelistForm form) {
        return secondEnrollmentWhitelistService.createWhitelist(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @PostMapping("/update")
    @Operation(summary = "修改白名单")
    public boolean update(@RequestBody @Valid SecondEnrollmentWhitelistForm form) {
        return secondEnrollmentWhitelistService.updateWhitelist(form);
    }
    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @PostMapping("/delete")
    @Operation(summary = "删除白名单")
    public boolean delete(@RequestBody @Valid KeyForm<Long> form) {
        return secondEnrollmentWhitelistService.deleteWhitelist(form.getKey());
    }
















//    /**
//     * 新增白名单
//     */
//    @AutoLog(operateType = OperateType.OPERATE_ADD)
//    @PostMapping("/create")
//    @Operation(summary = "新增白名单")
//    public RestMessage<Boolean> create(@RequestBody SecondEnrollmentWhitelistVo whitelist) {
//        // TODO：优化
//        // 检查身份证号是否已存在
//        SecondEnrollmentWhitelist existWhitelist = secondEnrollmentWhitelistService.getByIdCardNumber(whitelist.getIdCardNumber());
//        return ObjectUtil.isEmpty(existWhitelist) ? RestMessage.error("新增失败") : RestMessage.success();
//    }
//
//    /**
//     * 修改白名单
//     */
//    // TODO：优化 加上日志 显示什么操作
//    @AutoLog(operateType = OperateType.OPERATE_EDIT)
//    @PostMapping("/update")
//    @Operation(summary = "修改白名单")
//    // TODO：@RequestBody @Valid 判断数据
//    public RestMessage<Boolean> update(@RequestBody @Valid SecondEnrollmentWhitelist whitelist) {
//        // 检查身份证号是否已存在（排除当前记录）
//        boolean exists = secondEnrollmentWhitelistService.lambdaQuery()
//                .eq(SecondEnrollmentWhitelist::getIdCardNumber, whitelist.getIdCardNumber())
//                .ne(SecondEnrollmentWhitelist::getId, whitelist.getId())
//                .exists();
//        if (exists) {
//            return RestMessage.error("身份证号已存在");
//        }
//        return secondEnrollmentWhitelistService.updateById(whitelist)
//                ? RestMessage.success(true)
//                : RestMessage.error("修改失败");
//    }
//
//    /**
//     * 删除白名单
//     */
//    @AutoLog(operateType = OperateType.OPERATE_DELETE)
//    @PostMapping("/delete")
//    @Operation(summary = "删除白名单")
//    public RestMessage<Boolean> delete(@RequestBody SecondEnrollmentWhitelist whitelist) {
//        // 检查记录是否存在
//        SecondEnrollmentWhitelist existWhitelist = secondEnrollmentWhitelistService.getById(whitelist.getId());
//        if (existWhitelist == null) {
//            return RestMessage.error("记录不存在");
//        }
//        // 使用通用的逻辑删除方法
//        boolean success = secondEnrollmentWhitelistService.removeById(whitelist.getId());
//        return success ? RestMessage.success(true) : RestMessage.error("删除失败");
//    }

} 