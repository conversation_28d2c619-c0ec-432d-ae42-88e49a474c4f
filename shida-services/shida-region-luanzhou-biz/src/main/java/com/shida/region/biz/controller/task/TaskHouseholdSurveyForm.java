package com.shida.region.biz.controller.task;


import cn.hutool.core.util.ObjectUtil;
import com.shida.region.biz.entity.StudentBase;
import com.shida.region.biz.service.IStudentBaseInfoService;
import com.shida.region.biz.service.IStudentBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class TaskHouseholdSurveyForm {



    @Resource
    private IStudentBaseService studentBaseService;

    @Resource
    private IStudentBaseInfoService studentBaseInfoService;


    /**
     * 定时生成入户调查单
     * @throws IOException
     */
//    @Scheduled(cron = "0 */5 * * * ?")//每5分钟执行一次
//    @Scheduled(cron = "0 0/5 * * * ?")//测试每5分钟执行一次
    public void taskHouseholdSurveyForm()  {

        log.info("定时开始");
        List<StudentBase> list = studentBaseService.list();

        List<Long> idList = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {

            if(ObjectUtil.isEmpty(list.get(i).getPath())){
                idList.add(list.get(i).getId());
            }

        }

        if(!ObjectUtil.isEmpty(idList)){
            for (int j = 0; j < idList.size(); j++) {

                if(studentBaseInfoService.createEntryQuestionnaireForm(idList.get(j))){
                    log.info("定时任务 生成入户调查单成功");
                }else {
                    log.info("定时任务 生成入户调查单失败");
                }

            }
        }
        log.info("定时结束");
    }


    /**
     * 判断当前字符串，是不是 数字
     * @param str
     * @return
     */
    public static boolean isNumeric(String str){
        for (int i = str.length();--i>=0;){
            if (!Character.isDigit(str.charAt(i))){
                return false;
            }
        }
        return true;
    }

    public static boolean isNumericA(String s) {
        if (s != null && !"".equals(s.trim()))
            return s.matches("^[0-9]*$");
        else
            return false;
    }


    public static void main(String[] args) {

//        boolean numeric = isNumeric("1795998608581394434");
//        boolean numericA = isNumericA("1795998608581394434");
//
//        System.out.println(numeric);
//        System.out.println(numericA);

        Long aLong = Long.valueOf("1795998608581394434");
        System.out.println(aLong);

    }


}
