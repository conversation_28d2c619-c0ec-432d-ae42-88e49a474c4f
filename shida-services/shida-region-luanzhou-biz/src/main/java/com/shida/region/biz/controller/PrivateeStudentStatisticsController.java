package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.search.StudentStatisticsSearch;
import com.shida.region.biz.pojo.vo.StudentStatisticsInfo;
import com.shida.region.biz.service.IPrivateeStudentStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2025/4/13 15:31
 * @PackageName:com.shida.region.biz.controller
 * @ClassName: PrivateeStudentStatisticsController
 * @Version 1.0
 */
@Tag(name = "民办学生数据统计")
@RestController
@RequestMapping("/biz/studentPrivateeStatistics")
public class PrivateeStudentStatisticsController {

    @Resource
    private IPrivateeStudentStatisticsService privateeStudentStatisticsService;

    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @Operation(summary = "民办学生人数统计")
    @PostMapping("list")
    public PageInfo<StudentStatisticsInfo> getList(@RequestBody StudentStatisticsSearch search) {
        return privateeStudentStatisticsService.getDataList(search);
    }

    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @Operation(summary = "导出-民办学生人数统计")
    @PostMapping("exportStudent")
    public void exportStudent(HttpServletResponse response,StudentStatisticsSearch search) {
        privateeStudentStatisticsService.exportStudent(response, search);
    }
}
