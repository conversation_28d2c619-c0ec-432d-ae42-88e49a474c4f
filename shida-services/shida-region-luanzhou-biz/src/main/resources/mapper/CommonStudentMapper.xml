<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.CommonStudentMapper">

    <select id="getStudentInfoByIdCardNumber" resultType="com.shida.region.biz.pojo.vo.StudentVo">
        SELECT
        s.id AS studentId,
        s.user_id,
        s1.student_name,
        s1.student_id_card_number,
        s.school_type
        FROM r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        WHERE
        s.delete_flag = 0
        AND s1.student_id_card_number = #{studentIdCardNumber}
    </select>

    <select id="getStudentWeChatByIdCard" resultType="com.shida.region.biz.pojo.vo.StudentWeChatVo">
        SELECT u.nickname, s.school_type
        FROM `${regionCenterDbName}`.r_student_base AS s
         INNER JOIN `${regionCenterDbName}`.r_student_base_info AS s1 ON s.id = s1.student_id
         INNER JOIN `${dbName}`.t_user AS u ON s.user_id = u.id
        WHERE s1.student_id_card_number = #{idCard}
          AND s.delete_flag = 0
          and s.user_id is not null
    </select>

    <select id="selectStudentInfoById" resultType="com.shida.pojo.vo.StudentInfo">
        SELECT
            rsb.id AS student_id,
            rsbi.student_name AS student_name,
            rsbi.student_id_card_number AS id_card,
            rsb.enroll_school_id AS school_id,
            rsb.enroll_school_name AS school_name,
            rsb.is_related AS twins_flag,
            rsb.school_type as schoolType
        FROM
            r_student_base rsb
        LEFT JOIN r_student_base_info rsbi ON rsb.id = rsbi.student_id
        WHERE
            rsbi.student_id_card_number = #{idCard}
            and rsb.school_type = #{schoolType}
          AND rsb.delete_flag = 0
          AND rsbi.delete_flag = 0
    </select>

    <select id="selectStuInfoByIdCardAndSchoolType" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT rsb.user_id,
         rsb.id,
         rsb.house_info_type,
         rsb.is_related,
         rsb.related_num,r
         sb.enroll_school_id,
         rsb.enroll_school_name,
         rsb.school_type
        FROM r_student_base rsb
                 LEFT JOIN r_student_base_info rsbi
                           ON rsb.id = rsbi.student_id
        WHERE rsbi.student_id_card_number = #{studentIdCardNumber}
          AND rsbi.delete_flag = 0
          AND rsb.delete_flag = 0
    </select>


    <select id="getSchoolStatistic" resultType="com.shida.region.biz.pojo.vo.StudentStatisticsInfo">
        select t.schoolId,t.schoolName, t.type,t.nature, t.period, t.plan_num, IFNULL(t.signNum,0) AS signNum
        from (

            select t1.id as schoolId, t1.dept_name as schoolName, t1.type,t1.nature, t1.period, t1.plan_num,
            ( select
            sum(case when tr.is_related = 1 then (1 + IFNULL(tr.related_num,0))  else 1 end)
            from r_student_base tr
            where tr.delete_flag = 0
                and tr.enroll_school_id = t1.id
            <if test="search.type != null and search.type != ''">
                and tr.school_type = #{search.type}
            </if>
            <if test="search.period != null and search.period != ''">
                and tr.enroll_stage = #{search.period}
            </if>

            ) as signNum

        from `${dbName}`.t_dept t1
        where t1.level = 3 and t1.parent_id = #{search.deptId}
        <if test="search.schoolId != null and search.schoolId != ''">
            and t1.id = #{search.schoolId}
        </if>
        <if test="search.type != null and search.type != ''">
            and t1.type = #{search.type}
        </if>
        <if test="search.period != null and search.period != ''">
            and t1.period = #{search.period}
        </if>
        GROUP BY schoolId,schoolName,t1.type,t1.nature,t1.period
        ) t
        order by t.schoolId
    </select>

    <select id="getSchoolStatisticList"  resultType="com.shida.region.biz.pojo.vo.StudentStatisticsInfo">
        select t.schoolId,t.schoolName, t.type, t.nature,t.period, t.plan_num, IFNULL(t.signNum,0) AS signNum
        from (

        select t1.id as schoolId, t1.dept_name as schoolName, t1.type,t1.nature, t1.period, t1.plan_num,
        ( select
        sum(case when tr.is_related = 1 then (1 + IFNULL(tr.related_num,0))  else 1 end)
        from r_student_base tr
        where tr.delete_flag = 0
        and tr.enroll_school_id = t1.id
        <if test="search.type != null and search.type != ''">
            and tr.school_type = #{search.type}
        </if>
        <if test="search.period != null and search.period != ''">
            and tr.enroll_stage = #{search.period}
        </if>

        ) as signNum

        from `${dbName}`.t_dept t1
        where t1.level = 3 and t1.parent_id = #{search.deptId}
        <if test="search.schoolId != null and search.schoolId != ''">
            and t1.id = #{search.schoolId}
        </if>
        <if test="search.type != null and search.type != ''">
            and t1.type = #{search.type}
        </if>
        <if test="search.period != null and search.period != ''">
            and t1.period = #{search.period}
        </if>
        GROUP BY schoolId,schoolName,t1.type,t1.nature,t1.period
        ) t
        order by t.schoolId
    </select>

    <select id="getCountPrivateeSignNumByPeriod" resultType="java.lang.Integer">
        select IFNULL(sum(case when tr.is_related = 1 then (1 + IFNULL(tr.related_num,0))  else 1 end), 0)
        from r_student_base tr
        where tr.delete_flag = 0
        AND tr.school_type = '2'
        AND tr.delay_Type IS NULL
        AND tr.outside_School_Type is null
        <if test="period != null and period != ''">
            and tr.enroll_stage = #{period}
        </if>
    </select>


</mapper>
