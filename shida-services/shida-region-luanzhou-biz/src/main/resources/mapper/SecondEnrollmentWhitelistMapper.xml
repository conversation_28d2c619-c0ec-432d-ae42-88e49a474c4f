<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.SecondEnrollmentWhitelistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shida.region.biz.entity.SecondEnrollmentWhitelist">
        <id column="id" property="id" />
        <result column="student_name" property="studentName" />
        <result column="id_card_number" property="idCardNumber" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, student_name, id_card_number, create_time, update_time, version, delete_flag
    </sql>
    
    <update id="updateDeleteFlag">
        UPDATE t_second_enrollment_whitelist SET delete_flag = 1 WHERE id = #{id}
    </update>

    <select id="selectSecondEnrollmentWhitelistId" resultType="String">
        select id from t_second_enrollment_whitelist
        where student_name = #{arg0}
          and id_card_number = #{arg1}
          and delete_flag = 0
    </select>
</mapper> 