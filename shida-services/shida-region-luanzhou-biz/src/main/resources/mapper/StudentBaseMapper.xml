<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.StudentBaseMapper">

    <sql id="Base_Column_List">
        `id`
        , `user_id`, `set_up_save_ids`, `enroll_time`, `enroll_school_id`, `enroll_school_name`, `school_review_status`,
       `school_review_reason`, `education_review_status`, `education_review_reason`, `estate_review_status`, `estate_review_reason`,
       `public_security_review_status`, `public_security_review_reason`, `adjust_school_id`, `adjust_school_name`, `check_in_status`,
        `un_check_in_reason`, `emroll_stage`, `enroll_source`, `create_time`, `update_time`, `version`, `delete_flag`
    </sql>

    <sql id="getPublicSecurityAndHousePropertySql">
        SELECT
        s.id AS student_base_id,
        s.house_info_type,
        s.enroll_stage,
        s.enroll_id,
        s1.student_name,
        s1.student_id_card_number,
        s.public_security_review_status,
        s.estate_review_status,
        rri.account_number,
        rri.household_name,
        rri.relation_of_student,
        rri.reserve_field1 AS huAddress,
        rsss.ids_name `type`,
        rgi.guardian1_phone,
        s.enroll_school_name
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        LEFT JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        WHERE
        s.delete_flag = 0
        AND s.adjustment = 0
        <if test="search.type == 0 ">
            <if test="search.publicSecurityReviewType == 1">
                AND s.set_up_save_ids in (18,26,74,82)
            </if>
            <if test="search.publicSecurityReviewType == 2">
                AND s.set_up_save_ids in (20,28,76,84)
            </if>
            <if test="null == search.publicSecurityReviewType">
                AND s.set_up_save_ids in (18,20,26,28,74,76,82,84)
            </if>
        </if>
        <if test="search.type == 1 ">
            AND s.house_info_type IS NOT NULL AND s.house_info_type != 0
        </if>
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </sql>
    <update id="updateDeleteFlag">
        UPDATE r_student_base
        SET delete_flag = #{deleteFlag},
            update_time = now()
        WHERE id = #{studentId}
    </update>
    <delete id="trueDeleteById">
        DELETE
        FROM r_student_base
        WHERE id = #{id}
    </delete>
    <select id="selectAllEnrollIdCardNumberRedis" resultType="java.lang.String">
        SELECT DISTINCT rsb.student_id_card_number
        FROM r_student_base_info  rsb
                 left JOIN r_student_base sb on rsb.student_id=sb.id
                 left join `tshg-user-center`.t_dept t on t.id =sb.enroll_school_id
        WHERE  rsb.delete_flag = 0 and sb.delete_flag=0
          and t.type=1
          AND rsb.create_time &lt;  '2025-08-02 08:58:00' and sb.outside_School_Type is NULL;
    </select>

    <select id="getPublicSecurityAndHousePropertyPage"
            resultType="com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo">
        SELECT
        s.id AS student_base_id,
        s.house_info_type,
        s.enroll_stage,
        s.enroll_id,
        s1.student_name,
        s1.student_id_card_number,
        s.public_security_review_status,
        s.estate_review_status,
        rri.account_number,
        rri.household_name,
        rri.relation_of_student,
        rri.reserve_field1 AS huAddress,
        rsss.ids_name `type`,
        rgi.guardian1_phone,
        s.enroll_school_name
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        LEFT JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        WHERE
        s.delete_flag = 0
        AND s.adjustment = 0
        AND s.set_up_save_ids IN
        <foreach collection="setupIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="search.type == 1 ">
            AND s.house_info_type IS NOT NULL AND s.house_info_type != 0
        </if>
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </select>

    <select id="getHukouPage"
            resultType="com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo">
        SELECT
        s.id AS student_base_id,
        s.house_info_type,
        s.enroll_stage,
        s.enroll_id,
        s1.student_name,
        s1.student_id_card_number,
        s.public_security_review_status,
        s.estate_review_status,
        rri.account_number,
        rri.household_name,
        rri.relation_of_student,
        rri.reserve_field1 AS huAddress,
        rsss.ids_name `type`,
        rgi.guardian1_phone,
        s.enroll_school_name
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        inner JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        WHERE
        s.delete_flag = 0
        AND s.adjustment = 0
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </select>

    <select id="getJuzhuPage"
            resultType="com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo">
        SELECT
        s.id AS student_base_id,
        s.house_info_type,
        s.enroll_stage,
        s.enroll_id,
        s1.student_name,
        s1.student_id_card_number,
        s.public_security_residence_review_status as publicSecurityReviewStatus,
        s.estate_review_status,
        rri.account_number,
        rri.household_name,
        rri.relation_of_student,
        rri.reserve_field1 AS huAddress,
        rsss.ids_name `type`,
        rgi.guardian1_phone,
        s.enroll_school_name,
        juzhu.residence_permit_number as certificateCode
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        LEFT JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        inner join r_residence_info juzhu on juzhu.student_id = s.id
        WHERE
        s.delete_flag = 0
        AND s.adjustment = 0
        <if test="search.type == 1 ">
            AND s.house_info_type IS NOT NULL AND s.house_info_type != 0
        </if>
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </select>



    <select id="getPublicSecurityList" resultType="com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo">
        <include refid="getPublicSecurityAndHousePropertySql"/>
    </select>



    <select id="getStudentInfoByIdCardNumber" resultType="com.shida.region.biz.pojo.vo.StudentVo">
        SELECT s.id AS studentId, s.user_id, s1.student_name, s1.student_id_card_number
        FROM r_student_base AS s
                 INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        WHERE s.delete_flag = 0 and s.school_type = '1'
          AND student_id_card_number = #{studentIdCardNumber}
    </select>

    <select id="getStudentInfoByEnrollId" resultType="com.shida.region.biz.pojo.vo.StudentVo">
        SELECT s.id AS studentId, s.user_id, s1.student_name, s1.student_id_card_number
        FROM r_student_base AS s
                 INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        WHERE s.delete_flag = 0
          AND enroll_id = #{enrollId}
    </select>

    <select id="getStudentWeChatByUserId" resultType="com.shida.region.biz.pojo.vo.StudentWeChatVo">
        SELECT u.nickname
        FROM `${regionCenterDbName}`.r_student_base AS s
                 INNER JOIN `${regionCenterDbName}`.r_student_base_info AS s1 ON s.id = s1.student_id
                 INNER JOIN `${dbName}`.t_user AS u ON s.user_id = u.id
        WHERE u.id = #{userId}
          AND s.delete_flag = 0 LIMIT 1
    </select>
    <select id="getUserByUserId" resultType="com.shida.region.biz.pojo.vo.StudentWeChatVo">
        SELECT u.nickname
        FROM `${dbName}`.t_user u
        WHERE u.id = #{userId}
    </select>

    <select id="getStudentInfoByStudentId" resultType="com.shida.region.biz.pojo.vo.StudentVo">
        SELECT s.id AS studentId
             , s.enroll_school_name
             , s.adjust_school_name
             , s1.student_name
             , s.enroll_id
             , s.school_review_status
             , s.education_review_status
             , s.enroll_stage
             , s.publicity_status
        FROM r_student_base AS s
                 INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        WHERE s.delete_flag = 0
          AND s.adjustment = 0
          AND s.id = #{studentId}
    </select>
    <select id="selectClearStudent" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT rsb.id, rsb.house_info_type,rsb.user_id,rsbi.student_id_card_number AS enroll_id
        FROM r_student_base rsb
                 LEFT JOIN r_student_base_info rsbi
                           ON rsb.id = rsbi.student_id
        WHERE rsb.id = #{id}
          AND rsb.clear_flag = 1
          AND rsb.delete_flag = 1
    </select>
    <select id="selectStuInfoByIdCard" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT rsb.user_id, rsb.id, rsb.house_info_type,rsb.is_related,rsb.related_num,rsb.enroll_school_id,rsb.enroll_school_name
        FROM r_student_base rsb
                 LEFT JOIN r_student_base_info rsbi
                           ON rsb.id = rsbi.student_id
        WHERE rsbi.student_id_card_number = #{studentIdCardNumber}
          AND rsbi.delete_flag = 0
          AND rsb.delete_flag = 0
    </select>
    <select id="getStudentTatal" resultType="com.shida.region.biz.pojo.vo.StudentTotalVo">
        SELECT SUM(CASE
                       WHEN set_up_save_ids IN (17, 18, 19, 20, 21, 22, 23, 24) THEN 1
                       ELSE 0 END)                                                                  AS villagesPrimarySchoolNum,
               SUM(CASE
                       WHEN set_up_save_ids IN (25, 26, 27, 28, 29, 30, 31, 32) THEN 1
                       ELSE 0 END)                                                                  AS villagesJuniorSchoolNum,
               SUM(CASE
                       WHEN set_up_save_ids IN (73, 74, 75, 76, 77, 78, 79, 80) THEN 1
                       ELSE 0 END)                                                                  AS urbanPrimarySchoolNum,
               SUM(CASE
                       WHEN set_up_save_ids IN (81, 82, 83, 84, 85, 86, 87, 88) THEN 1
                       ELSE 0 END)                                                                  AS urbanJuniorSchoolNum
        FROM r_student_base
        WHERE delete_flag = 0
          AND adjustment = 0
          AND delay_Type IS NULL
          AND outside_School_Type is null
          AND school_type = '1'
    </select>
    <select id="selectStudentInfoById" resultType="com.shida.pojo.vo.StudentInfo">
        SELECT
            rsb.id AS student_id,
            rsbi.student_name AS student_name,
            rsbi.student_id_card_number AS id_card,
            rsb.enroll_school_id AS school_id,
            rsb.enroll_school_name AS school_name,
            rsb.is_related AS twins_flag
        FROM
            r_student_base rsb
                LEFT JOIN r_student_base_info rsbi ON rsb.id = rsbi.student_id
        WHERE
            rsbi.student_id_card_number = #{idCard}
            and rsb.school_type = '1'
          AND rsb.delete_flag = 0
          AND rsbi.delete_flag = 0
    </select>
    <select id="selectAllEnrollIdCardNumber" resultType="java.lang.String">
        select  * from r_student_base s
                           LEFT JOIN r_student_base_info  si on s.id=si.student_id

        where outside_school_type IN (1, 2) and s.delete_flag=0 and student_id_card_number NOT LIKE 'swjd-%'
          and s.set_up_save_ids in (28 ,84);
    </select>
    <select id="selectParAllEnrollIdCardNumber" resultType="java.lang.String">
        select  * from r_student_base s
                           LEFT JOIN r_student_base_info  si on s.id=si.student_id

        where outside_school_type IN (1, 2) and s.delete_flag=0 and student_id_card_number NOT LIKE 'swjd-%'
          and s.set_up_save_ids in (1007);
    </select>
    <select id="selectStudentDelLogById" resultType="com.shida.region.biz.pojo.vo.StudentDelLogInfo">
        select *
        from `${dbName}`.`t_student_del_log`
        where id = #{id}
    </select>

    <update id="updateStudentDelLogRecoverFlag">
        UPDATE `${dbName}`.`t_student_del_log`
        SET
         `recover_flag` = '1'
         WHERE id = #{id}
    </update>

    <insert id="insertStudentDelLog">
        INSERT INTO `${dbName}`.`t_student_del_log`(`id`, `dept_id`, `dept_code`, `dept_name`, `school_id`, `school_name`,
                                                    `student_id`, `student_name`, `twins_flag`, `id_card`,
                                                     `create_time`,`creator`, `creator_name`, `version`, `school_type`)
        VALUES
            (
                #{info.id},  #{info.deptId}, #{info.deptCode}, #{info.deptName},#{info.schoolId},#{info.schoolName},
                #{info.studentId}, #{info.studentName}, #{info.twinsFlag},
                #{info.idCard}, #{info.createTime}, #{info.creator},#{info.creatorName},1,#{info.schoolType}
            )
    </insert>
    <delete id="deleteStudentDelLog">
        DELETE FROM `${dbName}`.`t_student_del_log`
        WHERE student_id = #{studentId}
    </delete>

    <insert id="insertStudentDelLogBatch">
        INSERT INTO `${dbName}`.`t_student_del_log`(`id`, `dept_id`, `dept_code`, `dept_name`, `school_id`, `school_name`,
        `student_id`, `student_name`, `twins_flag`, `id_card`, `create_time`,`creator`, `creator_name`, `version`,`school_type`)
        VALUES
        <foreach collection="list" item="info"  index="index" separator=",">
            (
            #{info.id},  #{info.deptId}, #{info.deptCode}, #{info.deptName},#{info.schoolId},#{info.schoolName},
            #{info.studentId}, #{info.studentName}, #{info.twinsFlag},
            #{info.idCard}, #{info.createTime}, #{info.creator},#{info.creatorName},1,#{info.schoolType}
            )
        </foreach>
    </insert>
</mapper>
