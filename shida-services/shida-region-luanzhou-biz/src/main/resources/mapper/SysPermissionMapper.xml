<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.SysPermissionMapper">

    <update id="updatePrivateeStatus">
        UPDATE `${dbName}`.`t_dept`
        SET
         `privatee_status` = #{status}
         WHERE id = #{deptId}
    </update>
    <select id="selectPermissionCodeStatus" resultType="Long">
        SELECT `status` FROM `sys_permission` where permission_code ='REPEAT_STUDENT'
    </select>

</mapper>
