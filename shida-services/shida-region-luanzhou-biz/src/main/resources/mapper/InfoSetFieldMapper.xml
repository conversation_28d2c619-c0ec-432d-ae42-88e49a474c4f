<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.InfoSetFieldMapper">
    <sql id="Base_Column_List">
        id, set_up_save_ids, type_config_id, field_id, field_name, field_english, `type`, sample_img_url,is_necessary,is_show,sort,update_time,create_time,version
    </sql>
    <select id="selectSetField" resultType="com.shida.region.biz.entity.InfoSetField">
        SELECT isf.id,isf.set_up_save_ids,isf.type_config_id,isf.field_id,isf.field_name,isf.field_english,isf.type,
               isf.sample_img_url,isf.is_necessary,isf.is_show,isf.sort
        FROM info_set_field isf
        LEFT JOIN info_type_config itc ON isf.type_config_id = itc.id
        WHERE isf.set_up_save_ids = #{setUpSaveIds,jdbcType=BIGINT}
        AND itc.config_id = #{configId,jdbcType=BIGINT}
    </select>
    <select id="selectDeleteIdList" resultType="java.lang.Long">
        SELECT isf.id
        FROM info_set_field isf
        LEFT JOIN info_type_config itc
        ON isf.type_config_id = itc.id
        WHERE itc.config_id = #{configId,jdbcType=INTEGER}
        AND isf.set_up_save_ids = #{setUpSaveIds,jdbcType=BIGINT}
        AND isf.type_config_id IN
        <foreach collection="typeConfigIdShowList" item="typeConfigId" open="(" separator="," close=")">
            #{typeConfigId}
        </foreach>
    </select>
    <select id="selectEnrollField" resultType="com.shida.region.biz.pojo.dto.EnrollFieldDto">
        SELECT isf.id,isf.type_config_id,isf.field_id,isf.field_english,isf.field_name,isf.type,isf.sample_img_url,
               isf.is_necessary,isf.sort,ifm.info_verification_code,ifm.input_item_code
        FROM info_set_field isf
        LEFT JOIN info_field_management ifm
            ON isf.field_id = ifm.id
        WHERE isf.is_show = 1
            AND isf.set_up_save_ids = #{key,jdbcType=BIGINT}
            AND isf.type_config_id IN
        <foreach collection="typeConfigIds" item="typeConfigId" open="(" separator="," close=")">
            #{typeConfigId}
        </foreach>
        ORDER BY isf.sort
    </select>
</mapper>
