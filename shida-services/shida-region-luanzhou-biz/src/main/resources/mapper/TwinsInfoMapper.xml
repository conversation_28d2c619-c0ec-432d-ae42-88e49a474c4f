<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.TwinsInfoMapper">

    <update id="updateDeleteFlag">
        UPDATE r_twins_info
        SET delete_flag = 0
        WHERE student_id = #{studentId}
    </update>
    <delete id="deleteByStudentId">
        DELETE
        FROM r_twins_info
        WHERE student_id = #{studentId,jdbcType=BIGINT}
    </delete>
    <delete id="trueDeleteById">
        DELETE
        FROM r_twins_info
        WHERE student_id = #{studentId}
    </delete>
    <select id="selectClearTwinsInfo" resultType="com.shida.region.biz.entity.TwinsInfo">
        SELECT id, id_card_number
        FROM r_twins_info
        WHERE student_id = #{studentId}
          AND delete_flag = 1
    </select>
    <select id="getTwinsTatal" resultType="java.lang.Integer">
        SELECT s.set_up_save_ids
        FROM r_twins_info AS t
        LEFT JOIN r_student_base AS s ON s.id = t.student_id
        WHERE s.delete_flag = 0 AND t.delete_flag = 0  AND s.adjustment = 0
    </select>
    <select id="selectTwinsList" resultType="com.shida.region.biz.entity.TwinsInfo">
        SELECT
            id_card_number
        FROM
            r_twins_info
        WHERE
            delete_flag = 1
          AND (
            student_id = #{studentId})
    </select>
</mapper>
