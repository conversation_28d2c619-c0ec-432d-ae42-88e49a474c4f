<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.InfoSetExcelMapper">
    <select id="getStudentBaseInfoS" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT
            sb.id,
            sb.enroll_id,
            sb.set_up_save_ids,
            sb.is_related,
            CASE
                WHEN sb.set_up_save_ids IN (18, 26, 74, 82) THEN
                    sb.public_security_registration_review_status
                ELSE 4
                END AS public_security_registration_review_status,
            CASE
                WHEN sb.set_up_save_ids IN (20, 28, 76, 84) THEN
                    sb.public_security_residence_review_status
                ELSE 4
                END AS public_security_residence_review_status,
            sb.enroll_time,
            sb.enroll_school_id,
            sb.enroll_school_name,
            sb.school_review_status,
            sb.school_review_reason,
            sb.education_review_status,
            sb.education_review_reason,
            sb.estate_review_status,
            sb.estate_review_reason,
            sb.public_security_review_status,
            sb.public_security_review_reason,
            sb.check_in_status,
            sb.house_info_type,
            sb.adjust_school_id,
            sb.adjust_school_name,
            sb.adjustment,
            sb.school_username,
            sb.school_operation_time,
            sb.education_username,
            sb.education_operation_time,
            sb.set_up_save_ids,
            sb.un_check_in_reason,
            sb.admission_letter_in_status,
            sb.publicity_status
        FROM
            r_student_base AS sb
                LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
                LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
                LEFT JOIN `tshg-user-center`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE
            sb.delete_flag = 0
          AND (sb.adjustment = 0 OR sb.adjustment IS NULL)
          AND sb.school_type IN (1, 2)
          AND sb.enroll_stage = 3
          AND sb.outside_school_type IN (1, 2)
    </select>
    <select id="getStudentBaseInfo" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT
        sb.id,
        sb.enroll_id,
        sb.set_up_save_ids,
        sb.is_related,
public_security_registration_review_status,
  public_security_residence_review_status,
        <!--      sb.public_security_registration_review_status,
                sb.public_security_residence_review_status,-->
        sb.enroll_time,
        sb.enroll_school_id,
        sb.enroll_school_name,
        sb.school_review_status,
        sb.school_review_reason,
        sb.education_review_status,
        sb.education_review_reason,
        sb.estate_review_status,
        sb.estate_review_reason,
        sb.public_security_review_status,
        sb.public_security_review_reason,
        sb.check_in_status,
        sb.house_info_type,
        sb.adjust_school_id,
        sb.adjust_school_name,
        sb.adjustment,
        sb.school_username,
        sb.school_operation_time,
        sb.education_username,
        sb.education_operation_time,
        sb.set_up_save_ids,
        sb.un_check_in_reason,
        sb.admission_letter_in_status,
        sb.publicity_status
        FROM r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0
        and sb.outside_School_Type is NULL
        <if test="search.spanStatus == 0 ">
            AND (sb.adjustment = 0 OR sb.adjustment IS NULL)
        </if>
        <if test="search.spanStatus == 1 ">
            AND sb.adjustment = 1
        </if>
        <if test="search.schoolType !=null and  search.schoolType !='' ">
            AND sb.school_type = #{search.schoolType}
        </if>
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != '' or search.publicityStatus == 0">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--是否多胞胎-->
        <if test="search.isRelated != null and search.isRelated != ''">
            AND sb.is_related = #{search.isRelated}
        </if>
        <!--公安户口审核-->
        <if test="search.publicSecurityRegistrationReviewStatus != null and search.publicSecurityRegistrationReviewStatus != ''">
            AND sb.public_security_registration_review_status = #{search.publicSecurityRegistrationReviewStatus}
        </if>
        <!--公安居住证审核-->
        <if test="search.publicSecurityResidenceReviewStatus != null and search.publicSecurityResidenceReviewStatus != ''">
            AND sb.public_security_residence_review_status = #{search.publicSecurityResidenceReviewStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>

        <!--学校名称-->
        <if test="search.enrollSchoolName != null and search.enrollSchoolName != ''">
            AND sb.enroll_school_name like CONCAT('%',#{search.enrollSchoolName},'%')
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--类别
        AND sb.set_up_save_ids = #{search.type}
         <if test="search.houseInfoType != null and search.houseInfoType != ''">
            AND sb.house_info_type = #{search.houseInfoType}
        </if>
        -->
        <if test="search.type != null and search.type != ''">
            AND save.ids_name like CONCAT('%',#{search.type},'%')
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--教育局审核员-->
        <if test="search.schoolIds != null and search.schoolIds != ''">
            AND sb.enroll_school_id in
            <foreach collection="search.schoolIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getStudentBaseInfo2Public" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT
        s.id,
        s.enroll_id,
        s.set_up_save_ids,
        s.is_related,
        CASE
        WHEN s.set_up_save_ids IN (18,26,74,82)
        THEN s.public_security_registration_review_status
        ELSE 4
        END AS public_security_registration_review_status,
        CASE
        WHEN s.set_up_save_ids IN (20,28,76,84)
        THEN s.public_security_residence_review_status
        ELSE 4
        END AS public_security_residence_review_status,
        <!--      s.public_security_registration_review_status,
                s.public_security_residence_review_status,-->
        s.enroll_time,
        s.enroll_school_id,
        s.enroll_school_name,
        s.school_review_status,
        s.school_review_reason,
        s.education_review_status,
        s.education_review_reason,
        s.estate_review_status,
        s.estate_review_reason,
        s.public_security_review_status,
        s.public_security_review_reason,
        s.check_in_status,
        s.house_info_type,
        s.adjust_school_id,
        s.adjust_school_name,
        s.adjustment,
        s.school_username,
        s.school_operation_time,
        s.education_username,
        s.education_operation_time,
        s.set_up_save_ids,
        s.un_check_in_reason,
        s.admission_letter_in_status,
        s.publicity_status
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        LEFT JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        WHERE s.delete_flag = 0 AND s.adjustment = 0
        <if test="search.type == 0 ">
            <if test="search.publicSecurityReviewType == 1">
                AND s.set_up_save_ids in (18,26,74,82)
            </if>
            <if test="search.publicSecurityReviewType == 2">
                AND s.set_up_save_ids in (20,28,76,84)
            </if>
            <if test="null == search.publicSecurityReviewType">
                AND s.set_up_save_ids in (18,20,26,28,74,76,82,84)
            </if>
        </if>
        <if test="search.type == 1 ">
            AND s.house_info_type IS NOT NULL AND s.house_info_type != 0
        </if>
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </select>

    <select id="getStudentBaseInfo2PublicHukou" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT
        s.id,
        s.enroll_id,
        s.set_up_save_ids,
        s.is_related,
        s.public_security_registration_review_status,
        s.public_security_residence_review_status,
        s.enroll_time,
        s.enroll_school_id,
        s.enroll_school_name,
        s.school_review_status,
        s.school_review_reason,
        s.education_review_status,
        s.education_review_reason,
        s.estate_review_status,
        s.estate_review_reason,
        s.public_security_review_status,
        s.public_security_review_reason,
        s.check_in_status,
        s.house_info_type,
        s.adjust_school_id,
        s.adjust_school_name,
        s.adjustment,
        s.school_username,
        s.school_operation_time,
        s.education_username,
        s.education_operation_time,
        s.set_up_save_ids,
        s.un_check_in_reason,
        s.admission_letter_in_status,
        s.publicity_status
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        inner JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        WHERE s.delete_flag = 0 AND s.adjustment = 0
        <if test="search.type == 1 ">
            AND s.house_info_type IS NOT NULL AND s.house_info_type != 0
        </if>
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </select>

    <select id="getStudentBaseInfo2PublicJuzhu" resultType="com.shida.region.biz.entity.StudentBase">
        SELECT
        s.id,
        s.enroll_id,
        s.set_up_save_ids,
        s.is_related,
        s.public_security_registration_review_status,
        s.public_security_residence_review_status,
        s.enroll_time,
        s.enroll_school_id,
        s.enroll_school_name,
        s.school_review_status,
        s.school_review_reason,
        s.education_review_status,
        s.education_review_reason,
        s.estate_review_status,
        s.estate_review_reason,
        s.public_security_review_status,
        s.public_security_review_reason,
        s.check_in_status,
        s.house_info_type,
        s.adjust_school_id,
        s.adjust_school_name,
        s.adjustment,
        s.school_username,
        s.school_operation_time,
        s.education_username,
        s.education_operation_time,
        s.set_up_save_ids,
        s.un_check_in_reason,
        s.admission_letter_in_status,
        s.publicity_status
        FROM
        r_student_base AS s
        INNER JOIN r_student_base_info AS s1 ON s.id = s1.student_id
        LEFT JOIN r_registration_info AS rri ON s.id = rri.student_id
        LEFT JOIN r_guardian_info AS rgi ON s.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS rsss ON s.set_up_save_ids = rsss.setup_id
        inner join r_residence_info juzhu on juzhu.student_id = s.id
        WHERE s.delete_flag = 0 AND s.adjustment = 0
        <if test="search.type == 1 ">
            AND s.house_info_type IS NOT NULL AND s.house_info_type != 0
        </if>
        <if test="search.enrollId!=null and search.enrollId!=''">
            AND s.enroll_id = #{search.enrollId}
        </if>
        <if test="search.keywords!=null and search.keywords!=''">
            AND (s1.student_name = #{search.keywords} OR s1.student_id_card_number = #{search.keywords})
        </if>
        <if test="search.type == 0 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.public_security_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.type == 1 and search.publicSecurityAndHouseReviewStatus!=null and search.publicSecurityAndHouseReviewStatus!=''">
            AND s.estate_review_status = #{search.publicSecurityAndHouseReviewStatus}
        </if>
        <if test="search.createdTime!=null">
            AND s.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime!=null">
            AND s.enroll_time &lt;= #{search.endTime}
        </if>
        <if test="search.applyType != null and search.applyType != '' ">
            AND rsss.ids_name = #{search.applyType}
        </if>
    </select>

</mapper>
